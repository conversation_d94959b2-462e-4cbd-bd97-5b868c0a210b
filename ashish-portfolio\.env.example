# Database (Required)
DATABASE_URL="postgresql://username:password@localhost:5432/portfolio_db"
# For production, use Supabase or Neon:
# DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"

# NextAuth.js (Required if using authentication)
NEXTAUTH_URL="http://localhost:3000"
# For production: NEXTAUTH_URL="https://your-domain.vercel.app"
NEXTAUTH_SECRET="your-secret-key-here-minimum-32-characters-long"

# OAuth Providers (if using)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Google Analytics (if using)
GOOGLE_ANALYTICS_PROPERTY_ID="your-ga-property-id"
GOOGLE_SERVICE_ACCOUNT_EMAIL="<EMAIL>"
GOOGLE_PRIVATE_KEY="your-private-key"

# Email Service (if using contact form)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Cloudinary (if using for image uploads)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Other API Keys
RESEND_API_KEY="your-resend-api-key"
