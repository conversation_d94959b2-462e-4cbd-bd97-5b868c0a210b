import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Vercel optimizations
  poweredByHeader: false,
  compress: true,

  // Experimental features for better performance
  experimental: {
    optimizePackageImports: ['lucide-react', '@tiptap/react'],
  },

  images: {
    domains: ["www.prisma.io","assets.vercel.com","res.cloudinary.com","images.unsplash.com"],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'assets.vercel.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow', // Prevent CMS from being indexed
          },
        ],
      },
    ];
  },

  // Redirects for custom domain
  async redirects() {
    return [
      {
        source: '/portfolio',
        destination: 'https://ashishkamat.com.np',
        permanent: true,
      },
      {
        source: '/site',
        destination: 'https://ashishkamat.com.np',
        permanent: true,
      },
        ],
      },
    ];
  },
};

export default nextConfig;
