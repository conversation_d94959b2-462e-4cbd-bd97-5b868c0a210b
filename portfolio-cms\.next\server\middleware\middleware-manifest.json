{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "30b1e7402cc5b5cbfbba393c2cf7b78e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2aa15c7a57351c154a7905c69cedc34574800d128552c15e56eadb0e2ac9b55d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "86ea2d2723cae714c56df9843d67b1a41aa7af84b99b62b7c1e6399177edb10c"}}}, "instrumentation": null, "functions": {}}