{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "domains": ["yourname.com", "www.yourname.com", "ashish.dev", "www.ashish.dev"], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "redirects": [{"source": "/admin", "destination": "https://admin.yourname.com", "permanent": true}, {"source": "/cms", "destination": "https://admin.yourname.com", "permanent": true}, {"source": "/dashboard", "destination": "https://admin.yourname.com", "permanent": true}]}