{"buildCommand": "npm run vercel-build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "regions": ["iad1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "domains": ["admin.yourname.com", "cms.yourname.com", "admin.ashish.dev", "cms.ashish.dev"], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "redirects": [{"source": "/portfolio", "destination": "https://yourname.com", "permanent": true}, {"source": "/site", "destination": "https://yourname.com", "permanent": true}]}